#!/usr/bin/env python3
"""
Simple Dashboard Data Structure Test
===================================
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_data_structure():
    """Test the data structure fix for interactive dashboard."""
    
    print("🔍 Testing Interactive Dashboard Data Structure Fix")
    print("=" * 55)
    
    # Simulate enhanced results (what we get from enhanced financial model)
    enhanced_results = {
        'kpis': {
            'IRR_project': 0.075,
            'IRR_equity': 0.057,
            'LCOE_eur_kwh': 0.062,
            'NPV_project': 4500000,
            'NPV_equity': 2100000
        },
        'cashflow': 'DataFrame_object',
        'ml_predictions': {'predictions': {'irr': 0.08}},
        'monte_carlo': {'statistics': {'mean': 0.075}}
    }
    
    print("✅ Enhanced Results Structure:")
    for key in enhanced_results.keys():
        print(f"   - {key}: {type(enhanced_results[key])}")
    
    # OLD WAY (Problematic) - Direct pass to dashboard
    print("\n❌ OLD WAY (Problematic):")
    print("   analysis_results = enhanced_results")
    print("   dashboard tries: analysis_results['financial']['kpis']")
    print("   result: KeyError - 'financial' key not found")
    
    # NEW WAY (Fixed) - Proper structure for dashboard
    print("\n✅ NEW WAY (Fixed):")
    analysis_results_for_dashboard = {
        'financial': enhanced_results,  # This is the key fix!
        'enhanced_features': {
            'ml_predictions': enhanced_results.get('ml_predictions'),
            'monte_carlo': enhanced_results.get('monte_carlo')
        }
    }
    
    print("   analysis_results = {")
    print("     'financial': enhanced_results,")
    print("     'enhanced_features': {...}")
    print("   }")
    print("   dashboard tries: analysis_results['financial']['kpis']")
    print("   result: SUCCESS - KPIs found!")
    
    # Test the access pattern that the dashboard uses
    print("\n🧪 Testing Dashboard Access Pattern:")
    
    try:
        # This is exactly what the dashboard code does:
        # financial = analysis_results.get('financial', {})
        # kpis = financial.get('kpis', {})
        
        financial = analysis_results_for_dashboard.get('financial', {})
        kpis = financial.get('kpis', {})
        cashflow = financial.get('cashflow')
        
        print(f"   - financial data found: {bool(financial)}")
        print(f"   - kpis data found: {bool(kpis)}")
        print(f"   - cashflow data found: {cashflow is not None}")
        
        if kpis:
            print(f"   - Project IRR: {kpis.get('IRR_project', 0):.1%}")
            print(f"   - Equity IRR: {kpis.get('IRR_equity', 0):.1%}")
            print(f"   - LCOE: {kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh")
            
        return bool(financial) and bool(kpis)
        
    except Exception as e:
        print(f"   ❌ Access test failed: {e}")
        return False

def test_chart_factory_compatibility():
    """Test compatibility with chart factory expectations."""
    
    print("\n📊 Testing Chart Factory Compatibility:")
    print("-" * 40)
    
    # This is what the chart factory expects
    expected_structure = {
        'financial': {
            'kpis': {'IRR_project': 0.075},
            'cashflow': 'DataFrame'
        }
    }
    
    # Our fixed structure
    enhanced_results = {'kpis': {'IRR_project': 0.075}, 'cashflow': 'DataFrame'}
    fixed_structure = {'financial': enhanced_results}
    
    print("Chart Factory expects:")
    print("  analysis_results['financial']['kpis']")
    print("  analysis_results['financial']['cashflow']")
    
    print("\nOur fixed structure provides:")
    print("  analysis_results['financial'] = enhanced_results")
    print("  analysis_results['financial']['kpis'] = enhanced_results['kpis']")
    print("  analysis_results['financial']['cashflow'] = enhanced_results['cashflow']")
    
    # Test compatibility
    try:
        financial = fixed_structure.get('financial', {})
        kpis = financial.get('kpis', {})
        cashflow = financial.get('cashflow')
        
        compatible = bool(financial) and bool(kpis) and (cashflow is not None)
        
        print(f"\n✅ Compatibility test: {'PASSED' if compatible else 'FAILED'}")
        return compatible
        
    except Exception as e:
        print(f"\n❌ Compatibility test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Simple Dashboard Data Structure Test")
    print("=" * 50)
    
    test1 = test_data_structure()
    test2 = test_chart_factory_compatibility()
    
    overall_success = test1 and test2
    
    print("\n" + "=" * 50)
    if overall_success:
        print("🎉 SUCCESS: Data structure fix is correct!")
        print("✅ Interactive dashboard will now get financial data")
        print("✅ Chart factory will find the expected data structure")
        print("✅ KPIs will be displayed in the HTML dashboard")
    else:
        print("❌ FAILURE: Data structure issues remain")
    
    print("\n📝 Summary:")
    print("The fix wraps enhanced_results in a 'financial' key:")
    print("  analysis_results = {'financial': enhanced_results}")
    print("This matches what the dashboard expects to find.")
    
    sys.exit(0 if overall_success else 1)
