#!/usr/bin/env python3
"""
Test Dashboard Update Fix
========================

Test script to verify that the dashboard updates correctly when using
"Generate Complete Analysis & Reports" vs "Run Model" buttons.
"""

import sys
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from services.enhanced_integration_service import get_integration_service
from services.financial_service import FinancialModelService

def test_dashboard_data_flow():
    """Test that dashboard gets data in both workflows."""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    print("🔍 Testing Dashboard Data Flow Fix")
    print("=" * 50)
    
    # Create test data
    client_profile = ClientProfile(
        company_name="Test Solar Company",
        project_name="Test Solar Project",
        project_location="Morocco",
        consultant="Test Consultant",
        consultant_website="www.test.com",
        tagline="Test tagline",
        report_date="2025-01-21"
    )
    
    assumptions = EnhancedProjectAssumptions(
        capacity_mw=10.0,
        production_mwh_year1=18000.0,
        capex_meur=8.5,
        ppa_price_eur_kwh=0.045,
        debt_ratio=0.75,
        discount_rate=0.08
    )
    
    # Test 1: Standard Financial Model (Run Model button)
    print("\n📊 Test 1: Standard Financial Model")
    print("-" * 30)
    
    financial_service = FinancialModelService()
    
    try:
        standard_results = financial_service.run_financial_model(assumptions)
        
        has_kpis = bool(standard_results.get('kpis'))
        cashflow_data = standard_results.get('cashflow')
        has_cashflow = cashflow_data is not None and (
            (hasattr(cashflow_data, 'empty') and not cashflow_data.empty) or
            (isinstance(cashflow_data, dict) and len(cashflow_data) > 0)
        )
        
        print(f"✅ Standard model executed successfully")
        print(f"   - Has KPIs: {has_kpis}")
        print(f"   - Has Cashflow: {has_cashflow}")
        
        if has_kpis:
            kpis = standard_results['kpis']
            print(f"   - Project IRR: {kpis.get('IRR_project', 0):.1%}")
            print(f"   - Equity IRR: {kpis.get('IRR_equity', 0):.1%}")
            print(f"   - LCOE: {kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh")
        
    except Exception as e:
        print(f"❌ Standard model failed: {e}")
        return False
    
    # Test 2: Enhanced Comprehensive Analysis (Generate Complete Analysis button)
    print("\n🚀 Test 2: Enhanced Comprehensive Analysis")
    print("-" * 40)
    
    integration_service = get_integration_service()
    
    try:
        project_data = {
            'client_profile': client_profile.to_dict(),
            'assumptions': assumptions.to_dict()
        }
        
        enhanced_results = integration_service.run_enhanced_financial_model(
            project_data=project_data,
            include_ml_predictions=True,
            include_monte_carlo=True
        )
        
        has_kpis = bool(enhanced_results.get('kpis'))
        cashflow_data = enhanced_results.get('cashflow')
        has_cashflow = cashflow_data is not None and (
            (hasattr(cashflow_data, 'empty') and not cashflow_data.empty) or
            (isinstance(cashflow_data, dict) and len(cashflow_data) > 0)
        )
        has_ml = bool(enhanced_results.get('ml_predictions'))
        
        print(f"✅ Enhanced model executed successfully")
        print(f"   - Has KPIs: {has_kpis}")
        print(f"   - Has Cashflow: {has_cashflow}")
        print(f"   - Has ML Predictions: {has_ml}")
        
        if has_kpis:
            kpis = enhanced_results['kpis']
            print(f"   - Project IRR: {kpis.get('IRR_project', 0):.1%}")
            print(f"   - Equity IRR: {kpis.get('IRR_equity', 0):.1%}")
            print(f"   - LCOE: {kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh")
        
        if has_ml:
            ml_preds = enhanced_results['ml_predictions']
            print(f"   - ML Predictions: {len(ml_preds.get('predictions', {}))}")
        
    except Exception as e:
        print(f"❌ Enhanced model failed: {e}")
        return False
    
    # Test 3: Data Structure Comparison
    print("\n🔍 Test 3: Data Structure Comparison")
    print("-" * 35)
    
    print("Standard Results Structure:")
    for key in standard_results.keys():
        print(f"   - {key}: {type(standard_results[key])}")
    
    print("\nEnhanced Results Structure:")
    for key in enhanced_results.keys():
        print(f"   - {key}: {type(enhanced_results[key])}")
    
    # Verify both have the same core financial data
    both_have_kpis = 'kpis' in standard_results and 'kpis' in enhanced_results

    # Check cashflow more carefully
    std_cashflow = standard_results.get('cashflow')
    enh_cashflow = enhanced_results.get('cashflow')
    both_have_cashflow = (
        std_cashflow is not None and enh_cashflow is not None and
        ((hasattr(std_cashflow, 'empty') and not std_cashflow.empty) or isinstance(std_cashflow, dict)) and
        ((hasattr(enh_cashflow, 'empty') and not enh_cashflow.empty) or isinstance(enh_cashflow, dict))
    )
    
    print(f"\n✅ Both have KPIs: {both_have_kpis}")
    print(f"✅ Both have Cashflow: {both_have_cashflow}")
    
    if both_have_kpis and both_have_cashflow:
        print("\n🎉 SUCCESS: Both workflows produce compatible financial data!")
        print("   The dashboard should now display KPIs in both cases.")
        return True
    else:
        print("\n❌ ISSUE: Data structure mismatch detected!")
        return False

if __name__ == "__main__":
    success = test_dashboard_data_flow()
    sys.exit(0 if success else 1)
