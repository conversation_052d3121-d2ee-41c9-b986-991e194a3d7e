#!/usr/bin/env python3
"""
Enhanced History Functionality Test
===================================

Test script to verify comprehensive data restoration including all financial parameters and incentives.
"""

import sys
import os
import tempfile
import shutil
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from services.persistence_service import DataPersistenceService, ProjectData


def create_comprehensive_test_data():
    """Create comprehensive test data with all financial parameters and incentives."""
    
    # Create detailed client profile
    client_profile = ClientProfile(
        company_name="Green Energy Solutions SpA",
        client_name="<PERSON>",
        contact_email="<EMAIL>",
        phone="+39 06 1234567",
        project_name="Puglia Solar Farm - Phase II",
        consultant="Agevolami SRL",
        industry_sector="Renewable Energy",
        project_location="Puglia, Italy",
        project_capacity_mw=25.0,
        preferred_currency="EUR",
        language="EN"
    )
    
    # Create comprehensive project assumptions with all financial parameters and grants
    project_assumptions = EnhancedProjectAssumptions(
        # Technical parameters
        technology_type="Solar PV",
        capacity_mw=25.0,
        production_mwh_year1=45000.0,
        degradation_rate=0.005,
        project_life_years=25,
        
        # Financial parameters
        capex_meur=21.25,
        opex_keuros_year1=450.0,
        ppa_price_eur_kwh=0.047,
        ppa_escalation=0.015,
        debt_ratio=0.75,
        interest_rate=0.055,
        debt_years=15,
        discount_rate=0.08,
        tax_rate=0.24,  # Italian corporate tax rate
        land_lease_eur_mw_year=2500.0,
        
        # Comprehensive grants - all four types
        grant_meur_italy=3.5,     # Italian renewable energy incentive
        grant_meur_masen=0.0,     # Not applicable for Italian project
        grant_meur_connection=1.2, # Grid connection support
        grant_meur_simest_africa=0.0,  # Not applicable for Italian project
    )
    
    return client_profile, project_assumptions


def test_comprehensive_save_and_restore():
    """Test comprehensive save and restore functionality."""
    print("🧪 Testing Enhanced History Functionality")
    print("=" * 50)
    
    # Create temporary database for testing
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test_projects.db")
        backup_dir = os.path.join(temp_dir, "backups")
        
        print(f"📁 Using temporary database: {db_path}")
        
        # Initialize persistence service
        persistence_service = DataPersistenceService(db_path, backup_dir)
        
        # Create comprehensive test data
        client_profile, project_assumptions = create_comprehensive_test_data()
        
        print("\n📊 Original Data Summary:")
        print(f"  Client: {client_profile.company_name}")
        print(f"  Project: {client_profile.project_name}")
        print(f"  Capacity: {project_assumptions.capacity_mw} MW")
        print(f"  CAPEX: €{project_assumptions.capex_meur}M")
        print(f"  PPA Price: €{project_assumptions.ppa_price_eur_kwh:.3f}/kWh")
        
        # Calculate and display grants
        total_grants = project_assumptions.calculate_total_grants()
        grant_types = []
        if project_assumptions.grant_meur_italy > 0:
            grant_types.append(f"Italian: €{project_assumptions.grant_meur_italy}M")
        if project_assumptions.grant_meur_masen > 0:
            grant_types.append(f"MASEN: €{project_assumptions.grant_meur_masen}M")
        if project_assumptions.grant_meur_connection > 0:
            grant_types.append(f"Connection: €{project_assumptions.grant_meur_connection}M")
        if project_assumptions.grant_meur_simest_africa > 0:
            grant_types.append(f"SIMEST: €{project_assumptions.grant_meur_simest_africa}M")
        
        print(f"  Grants: €{total_grants}M ({len(grant_types)} types)")
        for grant in grant_types:
            print(f"    • {grant}")
        
        # Save project data
        print("\n💾 Saving Project Data...")
        project_id = f"test_project_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        project_data = ProjectData(
            id=project_id,
            name=client_profile.project_name,
            client_profile=client_profile.to_dict(),
            project_assumptions=project_assumptions.to_dict(),
            description=f"Test project with comprehensive financial data and {len(grant_types)} grant types",
            tags=["test", "comprehensive", "financial_complete", "grants_included"]
        )
        
        save_success = persistence_service.save_project(project_data, auto_version=True)
        
        if save_success:
            print("✅ Project saved successfully!")
            
            # Get saved versions
            versions = persistence_service.get_project_versions(project_id)
            print(f"📋 Found {len(versions)} version(s)")
            
            if versions:
                version_num = versions[0]['version']
                print(f"🔍 Testing restore of version {version_num}")
                
                # Test restore functionality
                restored_data = persistence_service.load_project(project_id, version_num)
                
                if restored_data:
                    print("✅ Project data loaded successfully!")
                    
                    # Verify client profile restoration
                    restored_client = ClientProfile.from_dict(restored_data.client_profile)
                    client_match = (
                        restored_client.company_name == client_profile.company_name and
                        restored_client.project_name == client_profile.project_name and
                        restored_client.contact_email == client_profile.contact_email
                    )
                    
                    print(f"👤 Client Profile Restoration: {'✅ PASS' if client_match else '❌ FAIL'}")
                    
                    # Verify comprehensive financial parameter restoration
                    restored_assumptions = EnhancedProjectAssumptions.from_dict(restored_data.project_assumptions)
                    
                    # Test financial parameters
                    financial_tests = [
                        ("capacity_mw", project_assumptions.capacity_mw, restored_assumptions.capacity_mw),
                        ("capex_meur", project_assumptions.capex_meur, restored_assumptions.capex_meur),
                        ("opex_keuros_year1", project_assumptions.opex_keuros_year1, restored_assumptions.opex_keuros_year1),
                        ("ppa_price_eur_kwh", project_assumptions.ppa_price_eur_kwh, restored_assumptions.ppa_price_eur_kwh),
                        ("debt_ratio", project_assumptions.debt_ratio, restored_assumptions.debt_ratio),
                        ("interest_rate", project_assumptions.interest_rate, restored_assumptions.interest_rate),
                    ]
                    
                    print("\n💰 Financial Parameters Restoration:")
                    financial_pass = True
                    for param_name, original, restored in financial_tests:
                        match = abs(original - restored) < 1e-6
                        financial_pass = financial_pass and match
                        print(f"  • {param_name}: {'✅' if match else '❌'} {original} -> {restored}")
                    
                    # Test grants restoration (the key enhancement)
                    print("\n🎁 Grants & Incentives Restoration:")
                    grant_tests = [
                        ("grant_meur_italy", project_assumptions.grant_meur_italy, restored_assumptions.grant_meur_italy),
                        ("grant_meur_masen", project_assumptions.grant_meur_masen, restored_assumptions.grant_meur_masen),
                        ("grant_meur_connection", project_assumptions.grant_meur_connection, restored_assumptions.grant_meur_connection),
                        ("grant_meur_simest_africa", project_assumptions.grant_meur_simest_africa, restored_assumptions.grant_meur_simest_africa)
                    ]
                    
                    grants_pass = True
                    for grant_name, original, restored in grant_tests:
                        match = abs(original - restored) < 1e-6
                        grants_pass = grants_pass and match
                        status = "✅" if match else "❌"
                        print(f"  • {grant_name}: {status} €{original}M -> €{restored}M")
                    
                    # Calculate restored grants total
                    restored_total_grants = restored_assumptions.calculate_total_grants()
                    grants_total_match = abs(total_grants - restored_total_grants) < 1e-6
                    print(f"  • Total Grants: {'✅' if grants_total_match else '❌'} €{total_grants}M -> €{restored_total_grants}M")
                    
                    # Overall test results
                    print("\n📊 Test Results Summary:")
                    print(f"  Client Profile: {'✅ PASS' if client_match else '❌ FAIL'}")
                    print(f"  Financial Parameters: {'✅ PASS' if financial_pass else '❌ FAIL'}")
                    print(f"  Grants & Incentives: {'✅ PASS' if grants_pass and grants_total_match else '❌ FAIL'}")
                    
                    overall_pass = client_match and financial_pass and grants_pass and grants_total_match
                    print(f"\n🎯 OVERALL RESULT: {'✅ ALL TESTS PASSED!' if overall_pass else '❌ SOME TESTS FAILED'}")
                    
                    if overall_pass:
                        print("\n🚀 Enhanced history functionality is working correctly!")
                        print("   All financial parameters and incentives are being saved and restored properly.")
                    else:
                        print("\n⚠️  Some issues found in the restoration process.")
                        
                    return overall_pass
                    
                else:
                    print("❌ Failed to load project data")
                    return False
            else:
                print("❌ No versions found")
                return False
        else:
            print("❌ Failed to save project")
            return False


def test_multiple_versions():
    """Test multiple version scenarios with different grant configurations."""
    print("\n" + "=" * 50)
    print("🧪 Testing Multiple Versions with Different Grant Scenarios")
    print("=" * 50)
    
    with tempfile.TemporaryDirectory() as temp_dir:
        db_path = os.path.join(temp_dir, "test_versions.db")
        backup_dir = os.path.join(temp_dir, "backups")
        
        persistence_service = DataPersistenceService(db_path, backup_dir)
        project_id = "multi_version_test"
        
        # Create base data
        client_profile, _ = create_comprehensive_test_data()
        
        # Test scenarios with different grant configurations
        scenarios = [
            {
                "name": "No Grants Scenario",
                "grants": {"italy": 0.0, "masen": 0.0, "connection": 0.0, "simest": 0.0}
            },
            {
                "name": "Italian Only Scenario", 
                "grants": {"italy": 2.5, "masen": 0.0, "connection": 0.0, "simest": 0.0}
            },
            {
                "name": "Italian + Connection Scenario",
                "grants": {"italy": 3.5, "masen": 0.0, "connection": 1.2, "simest": 0.0}
            },
            {
                "name": "All Grants Scenario",
                "grants": {"italy": 3.5, "masen": 2.0, "connection": 1.2, "simest": 1.5}
            }
        ]
        
        saved_versions = []
        
        for i, scenario in enumerate(scenarios):
            print(f"\n📝 Scenario {i+1}: {scenario['name']}")
            
            # Create assumptions for this scenario
            assumptions = EnhancedProjectAssumptions(
                capacity_mw=20.0,
                capex_meur=17.0,
                grant_meur_italy=scenario['grants']['italy'],
                grant_meur_masen=scenario['grants']['masen'],
                grant_meur_connection=scenario['grants']['connection'],
                grant_meur_simest_africa=scenario['grants']['simest']
            )
            
            total_grants = assumptions.calculate_total_grants()
            print(f"   Total Grants: €{total_grants}M")
            
            # Save this version
            project_data = ProjectData(
                id=project_id,
                name=f"Multi-Version Test - {scenario['name']}",
                client_profile=client_profile.to_dict(),
                project_assumptions=assumptions.to_dict(),
                description=f"Test scenario: {scenario['name']} with €{total_grants}M in grants"
            )
            
            success = persistence_service.save_project(project_data, auto_version=True)
            if success:
                saved_versions.append((i+1, scenario['name'], total_grants))
                print(f"   ✅ Saved successfully")
            else:
                print(f"   ❌ Save failed")
        
        # Test version history
        print(f"\n📋 Testing Version History ({len(saved_versions)} versions saved)")
        versions = persistence_service.get_project_versions(project_id)
        print(f"   Found {len(versions)} versions in database")
        
        # Test restoring each version
        restoration_results = []
        for version_info in versions:
            version_num = version_info['version']
            restored_data = persistence_service.load_project(project_id, version_num)
            
            if restored_data:
                restored_assumptions = EnhancedProjectAssumptions.from_dict(restored_data.project_assumptions)
                restored_grants = restored_assumptions.calculate_total_grants()
                restoration_results.append((version_num, restored_grants))
                print(f"   ✅ Version {version_num}: €{restored_grants}M grants restored")
            else:
                print(f"   ❌ Version {version_num}: restoration failed")
        
        print(f"\n🎯 Multi-Version Test Results:")
        print(f"   Saved: {len(saved_versions)} scenarios")
        print(f"   Versions in DB: {len(versions)}")
        print(f"   Successfully restored: {len(restoration_results)}")
        
        success = len(saved_versions) == len(versions) == len(restoration_results)
        print(f"   Result: {'✅ ALL VERSIONS WORKING' if success else '❌ VERSION ISSUES DETECTED'}")
        
        return success


if __name__ == "__main__":
    print("🧪 Enhanced History Functionality Test Suite")
    print("=" * 60)
    
    try:
        # Run comprehensive test
        test1_result = test_comprehensive_save_and_restore()
        
        # Run multiple versions test
        test2_result = test_multiple_versions()
        
        # Final summary
        print("\n" + "=" * 60)
        print("📊 FINAL TEST SUMMARY")
        print("=" * 60)
        print(f"✅ Comprehensive Data Test: {'PASSED' if test1_result else 'FAILED'}")
        print(f"✅ Multiple Versions Test: {'PASSED' if test2_result else 'FAILED'}")
        
        overall_success = test1_result and test2_result
        print(f"\n🎯 OVERALL RESULT: {'🎉 ALL TESTS PASSED!' if overall_success else '⚠️ SOME TESTS FAILED'}")
        
        if overall_success:
            print("\n🚀 The enhanced history functionality is working perfectly!")
            print("   ✓ All financial parameters are saved and restored")
            print("   ✓ All grant types and incentives are preserved")
            print("   ✓ Multiple versions work correctly")
            print("   ✓ Complete data integrity maintained")
        
        sys.exit(0 if overall_success else 1)
        
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1) 