#!/usr/bin/env python3
"""
Test Interactive Dashboard Data Fix
==================================

Test script to verify that the interactive dashboard HTML gets proper financial data
after the comprehensive analysis fix.
"""

import sys
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from models.client_profile import ClientProfile
from models.project_assumptions import EnhancedProjectAssumptions
from services.enhanced_integration_service import get_integration_service
from services.export_service import ExportService

def test_interactive_dashboard_data():
    """Test that interactive dashboard gets proper financial data."""
    
    # Setup logging
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)
    
    print("🔍 Testing Interactive Dashboard Data Fix")
    print("=" * 50)
    
    # Create test data
    client_profile = ClientProfile(
        company_name="Test Solar Company",
        project_name="Test Solar Project",
        project_location="Morocco",
        consultant="Test Consultant",
        consultant_website="www.test.com",
        tagline="Test tagline",
        report_date="2025-01-21"
    )
    
    assumptions = EnhancedProjectAssumptions(
        capacity_mw=10.0,
        production_mwh_year1=18000.0,
        capex_meur=8.5,
        ppa_price_eur_kwh=0.045,
        debt_ratio=0.75,
        discount_rate=0.08
    )
    
    # Test 1: Generate Enhanced Financial Results
    print("\n🚀 Test 1: Generate Enhanced Financial Results")
    print("-" * 40)
    
    integration_service = get_integration_service()
    
    try:
        project_data = {
            'client_profile': client_profile.to_dict(),
            'assumptions': assumptions.to_dict()
        }
        
        enhanced_results = integration_service.run_enhanced_financial_model(
            project_data=project_data,
            include_ml_predictions=True,
            include_monte_carlo=True
        )
        
        has_kpis = bool(enhanced_results.get('kpis'))
        has_cashflow = enhanced_results.get('cashflow') is not None
        has_ml = bool(enhanced_results.get('ml_predictions'))
        
        print(f"✅ Enhanced model executed successfully")
        print(f"   - Has KPIs: {has_kpis}")
        print(f"   - Has Cashflow: {has_cashflow}")
        print(f"   - Has ML Predictions: {has_ml}")
        
        if has_kpis:
            kpis = enhanced_results['kpis']
            print(f"   - Project IRR: {kpis.get('IRR_project', 0):.1%}")
            print(f"   - Equity IRR: {kpis.get('IRR_equity', 0):.1%}")
            print(f"   - LCOE: {kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh")
        
    except Exception as e:
        print(f"❌ Enhanced model failed: {e}")
        return False
    
    # Test 2: Structure Data for Interactive Dashboard
    print("\n📊 Test 2: Structure Data for Interactive Dashboard")
    print("-" * 50)
    
    # This is the key fix - structure data the way the dashboard expects it
    analysis_results_for_dashboard = {
        'financial': enhanced_results,  # This is the critical fix!
        'enhanced_features': {
            'ml_predictions': enhanced_results.get('ml_predictions'),
            'monte_carlo': enhanced_results.get('monte_carlo')
        }
    }
    
    # Verify the structure
    financial_data = analysis_results_for_dashboard.get('financial', {})
    dashboard_kpis = financial_data.get('kpis', {})
    dashboard_cashflow = financial_data.get('cashflow')
    
    print(f"✅ Dashboard data structure created")
    print(f"   - analysis_results['financial'] exists: {bool(financial_data)}")
    print(f"   - analysis_results['financial']['kpis'] exists: {bool(dashboard_kpis)}")
    print(f"   - analysis_results['financial']['cashflow'] exists: {dashboard_cashflow is not None}")
    
    if dashboard_kpis:
        print(f"   - Dashboard will show Project IRR: {dashboard_kpis.get('IRR_project', 0):.1%}")
        print(f"   - Dashboard will show Equity IRR: {dashboard_kpis.get('IRR_equity', 0):.1%}")
        print(f"   - Dashboard will show LCOE: {dashboard_kpis.get('LCOE_eur_kwh', 0):.3f} EUR/kWh")
    
    # Test 3: Test Interactive Dashboard Export
    print("\n🌐 Test 3: Test Interactive Dashboard Export")
    print("-" * 40)
    
    export_service = ExportService()
    
    try:
        # Create temporary output directory
        import tempfile
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)
            
            # Export interactive dashboard with properly structured data
            dashboard_file = export_service.export_interactive_dashboard(
                client_profile=client_profile,
                assumptions=assumptions,
                analysis_results=analysis_results_for_dashboard,
                output_dir={'reports_dir': temp_path}
            )
            
            print(f"✅ Interactive dashboard exported successfully")
            print(f"   - File created: {dashboard_file.exists()}")
            print(f"   - File size: {dashboard_file.stat().st_size / 1024:.1f} KB")
            
            # Check if the HTML contains financial data
            if dashboard_file.exists():
                with open(dashboard_file, 'r', encoding='utf-8') as f:
                    html_content = f.read()
                
                # Look for key indicators that data was included
                has_irr_data = 'IRR' in html_content and str(dashboard_kpis.get('IRR_project', 0)) in html_content
                has_plotly = 'plotly' in html_content.lower()
                has_charts = 'chart' in html_content.lower()
                
                print(f"   - Contains IRR data: {has_irr_data}")
                print(f"   - Contains Plotly charts: {has_plotly}")
                print(f"   - Contains chart elements: {has_charts}")
                
                if has_irr_data and has_plotly and has_charts:
                    print(f"🎉 SUCCESS: Interactive dashboard contains financial data!")
                    return True
                else:
                    print(f"⚠️ WARNING: Dashboard may not contain all expected data")
                    return False
            else:
                print(f"❌ Dashboard file was not created")
                return False
                
    except Exception as e:
        print(f"❌ Dashboard export failed: {e}")
        return False

def test_data_structure_comparison():
    """Compare old vs new data structure for dashboard."""
    print("\n🔍 Test 4: Data Structure Comparison")
    print("-" * 35)
    
    # Old structure (problematic)
    old_structure = {
        'kpis': {'IRR_project': 0.075, 'IRR_equity': 0.057},
        'cashflow': 'DataFrame_object'
    }
    
    # New structure (fixed)
    new_structure = {
        'financial': {
            'kpis': {'IRR_project': 0.075, 'IRR_equity': 0.057},
            'cashflow': 'DataFrame_object'
        }
    }
    
    print("Old Structure (Problematic):")
    print("  analysis_results = {")
    print("    'kpis': {...},")
    print("    'cashflow': {...}")
    print("  }")
    print("  → Dashboard looks for analysis_results['financial']['kpis'] → NOT FOUND")
    
    print("\nNew Structure (Fixed):")
    print("  analysis_results = {")
    print("    'financial': {")
    print("      'kpis': {...},")
    print("      'cashflow': {...}")
    print("    }")
    print("  }")
    print("  → Dashboard looks for analysis_results['financial']['kpis'] → FOUND ✅")
    
    # Test the access pattern
    try:
        # This is what the dashboard tries to do
        financial_old = old_structure.get('financial', {})
        kpis_old = financial_old.get('kpis', {})
        
        financial_new = new_structure.get('financial', {})
        kpis_new = financial_new.get('kpis', {})
        
        print(f"\nAccess Test Results:")
        print(f"  Old structure KPIs found: {bool(kpis_old)}")
        print(f"  New structure KPIs found: {bool(kpis_new)}")
        
        return bool(kpis_new)
        
    except Exception as e:
        print(f"❌ Structure test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Testing Interactive Dashboard Data Fix")
    print("=" * 60)
    
    success1 = test_interactive_dashboard_data()
    success2 = test_data_structure_comparison()
    
    overall_success = success1 and success2
    
    print("\n" + "=" * 60)
    if overall_success:
        print("🎉 ALL TESTS PASSED!")
        print("✅ Interactive dashboard will now receive financial data")
        print("✅ Dashboard HTML will display KPIs and charts")
        print("✅ Comprehensive analysis generates working dashboard")
    else:
        print("❌ SOME TESTS FAILED!")
        print("⚠️ Interactive dashboard may still have data issues")
    
    sys.exit(0 if overall_success else 1)
