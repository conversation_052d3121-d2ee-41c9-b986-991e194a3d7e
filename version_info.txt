# UTF-8
VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=(3, 0, 0, 0),
    prodvers=(3, 0, 0, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo([
      StringTable(u'040904B0', [
        StringStruct(u'CompanyName', u'Hiel RnE Solutions'),
        StringStruct(u'FileDescription', u'Professional Financial Modeling Tool for Renewable Energy Projects'),
        StringStruct(u'FileVersion', u'3.0.0'),
        StringStruct(u'InternalName', u'HielRnEModeler'),
        StringStruct(u'LegalCopyright', u'© 2025 Hiel RnE Solutions'),
        StringStruct(u'OriginalFilename', u'HielRnEModeler.exe'),
        StringStruct(u'ProductName', u'Hiel Renewable Energy Financial Modeler'),
        StringStruct(u'ProductVersion', u'3.0.0')
      ])
    ]),
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
